# Cross-Platform Messaging Setup

Send and receive messages between Sociality, Telegram, and Discord.

## Quick Setup

### 1. Configure <PERSON><PERSON>s
```bash
cp .env.example .env
```

Edit `.env` with your bot tokens:
- **Telegram**: Get from @BotFather
- **Discord**: Get from Discord Developer Portal

### 2. Start System
```bash
npm start
```

This starts:
- Main app (localhost:5000)
- Federation registry (localhost:7300)
- Telegram service (localhost:7301)
- Discord service (localhost:7302)

## Usage

### 1. Create Room
1. Open localhost:5000
2. Switch to "Cross-Platform" mode
3. Create room → get room ID

### 2. Connect Platforms
- **Telegram**: Add bot to group, use `/join <room_id>`
- **Discord**: Use `/join <room_id>` slash command

### 3. Send Messages
- Type in any platform
- Messages appear on ALL platforms instantly

## Commands

**Telegram**: `/join <room_id>`, `/leave`, `/status`
**Discord**: `/join <room_id>`, `/leave`, `/status`
