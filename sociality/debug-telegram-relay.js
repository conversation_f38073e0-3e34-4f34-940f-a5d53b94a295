#!/usr/bin/env node

/**
 * Debug script to test Telegram relay functionality
 * This script helps diagnose why Telegram can't receive messages
 */

import axios from 'axios';

const TELEGRAM_SERVICE_URL = 'http://localhost:7301';
const FEDERATION_URL = 'http://localhost:7300';

async function debugTelegramRelay() {
  console.log('🔍 Debugging Telegram Relay Functionality...\n');

  try {
    // Test 1: Check if Telegram service is running
    console.log('1️⃣ Testing Telegram service health...');
    try {
      const healthResponse = await axios.get(`${TELEGRAM_SERVICE_URL}/health`);
      console.log('✅ Telegram service is healthy:', healthResponse.data);
    } catch (error) {
      console.log('❌ Telegram service is not running:', error.message);
      console.log('💡 Start the backend with: npm start');
      return;
    }
    console.log();

    // Test 2: Check federation registry
    console.log('2️⃣ Testing federation registry...');
    try {
      const federationResponse = await axios.get(`${FEDERATION_URL}/health`);
      console.log('✅ Federation registry is healthy:', federationResponse.data);
    } catch (error) {
      console.log('❌ Federation registry is not running:', error.message);
      return;
    }
    console.log();

    // Test 3: Check registered peers
    console.log('3️⃣ Checking registered peers...');
    try {
      const peersResponse = await axios.get(`${FEDERATION_URL}/federation/peers`);
      console.log('📋 Registered peers:', peersResponse.data);
      
      const telegramPeer = peersResponse.data.find(peer => peer.name === 'telegram');
      if (telegramPeer) {
        console.log('✅ Telegram peer is registered');
      } else {
        console.log('❌ Telegram peer is NOT registered');
      }
    } catch (error) {
      console.log('❌ Failed to get peers:', error.message);
    }
    console.log();

    // Test 4: Check registered rooms
    console.log('4️⃣ Checking registered rooms...');
    try {
      const roomsResponse = await axios.get(`${FEDERATION_URL}/federation/rooms`);
      console.log('📋 Registered rooms:', roomsResponse.data);
      
      if (roomsResponse.data.length === 0) {
        console.log('⚠️ No rooms are registered. You need to bind a Telegram group first.');
        console.log('💡 In Telegram, use: /bind test-room-123');
      }
    } catch (error) {
      console.log('❌ Failed to get rooms:', error.message);
    }
    console.log();

    // Test 5: Test Telegram relay endpoint directly
    console.log('5️⃣ Testing Telegram relay endpoint...');
    const testMessage = {
      roomId: 'test-room-123',
      message: {
        text: 'Test message from debug script',
        from: {
          displayName: 'Debug Script',
          platform: 'sociality'
        },
        timestamp: new Date().toISOString()
      }
    };

    try {
      const relayResponse = await axios.post(`${TELEGRAM_SERVICE_URL}/api/cross-platform/relay`, testMessage);
      console.log('✅ Telegram relay endpoint response:', relayResponse.data);
    } catch (error) {
      console.log('❌ Telegram relay endpoint failed:', error.response?.data || error.message);
      
      if (error.response?.status === 404) {
        console.log('💡 This means no Telegram chat is bound to room "test-room-123"');
        console.log('💡 Bind a Telegram group first: /bind test-room-123');
      }
    }
    console.log();

    // Test 6: Test federation relay to Telegram
    console.log('6️⃣ Testing federation relay to Telegram...');
    try {
      const federationRelayResponse = await axios.post(`${FEDERATION_URL}/federation/relay-message`, {
        roomId: 'test-room-123',
        message: {
          text: 'Test message via federation',
          from: {
            displayName: 'Federation Test',
            platform: 'sociality'
          },
          timestamp: new Date().toISOString()
        },
        originatingPlatform: 'http://localhost:5000'
      });
      console.log('✅ Federation relay response:', federationRelayResponse.data);
    } catch (error) {
      console.log('❌ Federation relay failed:', error.response?.data || error.message);
    }
    console.log();

    console.log('🎯 Debug Summary:');
    console.log('================');
    console.log('If Telegram can send but not receive messages, check:');
    console.log('1. ✅ Telegram service is running (port 7301)');
    console.log('2. ✅ Federation registry is running (port 7300)');
    console.log('3. ✅ Telegram peer is registered with federation');
    console.log('4. ✅ Room is bound to a Telegram group (/bind command)');
    console.log('5. ✅ Telegram relay endpoint responds correctly');
    console.log('6. ✅ Federation can relay messages to Telegram');
    console.log();
    console.log('💡 Common fixes:');
    console.log('- Restart backend: npm start');
    console.log('- Bind Telegram group: /bind room-id');
    console.log('- Check bot permissions in Telegram group');
    console.log('- Verify bot token is correct');

  } catch (error) {
    console.error('❌ Debug script failed:', error.message);
  }
}

// Run the debug
debugTelegramRelay();
