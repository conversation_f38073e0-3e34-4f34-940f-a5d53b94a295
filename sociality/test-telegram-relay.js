#!/usr/bin/env node

/**
 * Test script to verify Telegram relay functionality
 * This script simulates sending a message to Telegram
 */

import axios from 'axios';

const TELEGRAM_SERVICE_URL = 'http://localhost:7301';
const FEDERATION_URL = 'http://localhost:7300';

async function testTelegramRelay() {
  console.log('🧪 Testing Telegram Relay Functionality...\n');

  try {
    // Test 1: Check if services are running
    console.log('1️⃣ Checking service health...');
    
    try {
      const telegramHealth = await axios.get(`${TELEGRAM_SERVICE_URL}/health`);
      console.log('✅ Telegram service:', telegramHealth.data);
    } catch (error) {
      console.log('❌ Telegram service not running:', error.message);
      console.log('💡 Start backend with: npm start');
      return;
    }

    try {
      const federationHealth = await axios.get(`${FEDERATION_URL}/health`);
      console.log('✅ Federation registry:', federationHealth.data);
    } catch (error) {
      console.log('❌ Federation registry not running:', error.message);
      return;
    }
    console.log();

    // Test 2: Check registered rooms
    console.log('2️⃣ Checking registered rooms...');
    try {
      const roomsResponse = await axios.get(`${FEDERATION_URL}/federation/rooms`);
      console.log('📋 Registered rooms:', roomsResponse.data.length);
      
      if (roomsResponse.data.length === 0) {
        console.log('⚠️ No rooms registered. Bind a Telegram group first: /bind test-room-123');
      } else {
        roomsResponse.data.forEach(room => {
          console.log(`   - Room: ${room.roomId} (${room.name})`);
          console.log(`     Peers: ${room.peers?.join(', ')}`);
        });
      }
    } catch (error) {
      console.log('❌ Failed to get rooms:', error.message);
    }
    console.log();

    // Test 3: Test direct Telegram relay
    console.log('3️⃣ Testing direct Telegram relay...');
    const testRoomId = 'test-room-123';
    const testMessage = {
      roomId: testRoomId,
      message: {
        text: 'Test message from script to Telegram',
        from: {
          displayName: 'Test Script',
          platform: 'sociality',
          userId: 'test-user-123'
        },
        timestamp: new Date().toISOString()
      }
    };

    try {
      const relayResponse = await axios.post(`${TELEGRAM_SERVICE_URL}/api/cross-platform/relay`, testMessage);
      console.log('✅ Direct Telegram relay successful:', relayResponse.data);
    } catch (error) {
      console.log('❌ Direct Telegram relay failed:', error.response?.data || error.message);
      
      if (error.response?.status === 404) {
        console.log('💡 No Telegram chat bound to this room. Use: /bind test-room-123');
      } else if (error.response?.status === 503) {
        console.log('💡 Telegram bot not available. Check bot token.');
      }
    }
    console.log();

    // Test 4: Test federation relay to Telegram
    console.log('4️⃣ Testing federation relay to Telegram...');
    try {
      const federationMessage = {
        roomId: testRoomId,
        message: {
          text: 'Test message via federation to Telegram',
          from: {
            displayName: 'Federation Test',
            platform: 'discord',
            userId: 'discord-user-123'
          },
          timestamp: new Date().toISOString()
        },
        originatingPlatform: 'http://localhost:7302'
      };

      const federationResponse = await axios.post(`${FEDERATION_URL}/federation/relay-message`, federationMessage);
      console.log('✅ Federation relay successful:', federationResponse.data);
      
      // Check if Telegram was in the results
      const telegramResult = federationResponse.data.results?.find(r => 
        r.peer === 'http://localhost:7301' || r.peerUrl === 'http://localhost:7301'
      );
      
      if (telegramResult) {
        if (telegramResult.success) {
          console.log('✅ Message successfully delivered to Telegram');
        } else {
          console.log('❌ Message failed to deliver to Telegram:', telegramResult.error);
        }
      }
    } catch (error) {
      console.log('❌ Federation relay failed:', error.response?.data || error.message);
    }
    console.log();

    // Test 5: Check Telegram bot status
    console.log('5️⃣ Checking Telegram bot status...');
    try {
      const healthResponse = await axios.get(`${TELEGRAM_SERVICE_URL}/health`);
      const health = healthResponse.data;
      
      console.log(`Bot Active: ${health.botActive ? '✅' : '❌'}`);
      console.log(`Connected Chats: ${health.connectedChats || 0}`);
      
      if (!health.botActive) {
        console.log('💡 Bot not active. Check TELEGRAM_BOT_TOKEN in .env file');
      }
      
      if (health.connectedChats === 0) {
        console.log('💡 No chats connected. Use /bind command in Telegram');
      }
    } catch (error) {
      console.log('❌ Failed to get bot status:', error.message);
    }
    console.log();

    console.log('🎯 Test Summary:');
    console.log('================');
    console.log('For Telegram to receive messages:');
    console.log('1. ✅ Telegram service must be running (port 7301)');
    console.log('2. ✅ Bot must be active (valid token)');
    console.log('3. ✅ Telegram group must be bound to room (/bind command)');
    console.log('4. ✅ Federation registry must route to Telegram');
    console.log('5. ✅ Telegram relay endpoint must work');
    console.log();
    console.log('💡 If tests pass but messages still don\'t work:');
    console.log('   - Check bot permissions in Telegram group');
    console.log('   - Verify bot is admin in the group');
    console.log('   - Try /status command in Telegram');
    console.log('   - Restart backend: npm start');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testTelegramRelay();
