# Sociality Cross-Platform Messaging Configuration

# Main Server Configuration
PORT=5000
NODE_ENV=development

# MongoDB Configuration
MONGO_URI=mongodb+srv://Gin007:<EMAIL>/Sociality?retryWrites=true&w=majority

# JWT Configuration
JWT_SECRET=your_jwt_secret_here

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Cross-Platform Services Configuration
ENABLE_CROSS_PLATFORM=true
FEDERATION_ENABLED=true

# Federation Registry Configuration
FEDERATION_REGISTRY_URL=http://localhost:7300
FEDERATION_PORT=7300

# Platform URLs
PLATFORM_URL=http://localhost:5000
PLATFORM_NAME=sociality

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=YOUR_TELEGRAM_BOT_TOKEN
TELEGRAM_PORT=7301
TELEGRAM_PLATFORM_URL=http://localhost:7301

# Discord Bot Configuration
DISCORD_BOT_TOKEN=YOUR_DISCORD_BOT_TOKEN
DISCORD_PORT=7302
DISCORD_PLATFORM_URL=http://localhost:7302

# Cross-Platform Room Settings
DEFAULT_ALLOWED_PLATFORMS=sociality,telegram,discord
MAX_ROOM_PARTICIPANTS=100
ROOM_MESSAGE_HISTORY_LIMIT=1000

# Security Settings
CORS_ORIGIN=http://localhost:4000
SESSION_SECRET=your_session_secret_here

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/sociality.log
