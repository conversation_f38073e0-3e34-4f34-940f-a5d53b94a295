{"name": "sociality-backend", "version": "1.0.0", "description": "Backend for Sociality social media platform", "main": "server.js", "type": "module", "scripts": {"start": "cross-env NODE_ENV=production node server.js", "dev": "cross-env NODE_ENV=development nodemon -r dotenv/config server.js"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.40.0", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "cron": "^3.1.6", "date-fns": "^4.1.0", "discord.js": "^14.19.3", "dotenv": "^16.3.1", "express": "^4.18.2", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.1", "mongoose": "^7.4.0", "node-telegram-bot-api": "^0.66.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "react-icons": "^5.5.0", "socket.io": "^4.7.2", "uuid": "^9.0.1"}, "devDependencies": {"cross-env": "^7.0.3", "nodemon": "^3.0.1"}}