#!/usr/bin/env node

/**
 * Test script to verify federation registry persistence
 * This script tests that rooms and peers persist across restarts
 */

import axios from 'axios';

const FEDERATION_URL = 'http://localhost:7300';
const TEST_ROOM_ID = 'test-persistence-room';

async function testFederationPersistence() {
  console.log('🧪 Testing Federation Registry Persistence...\n');

  try {
    // Test 1: Check if federation registry is running
    console.log('1️⃣ Testing federation registry health...');
    const healthResponse = await axios.get(`${FEDERATION_URL}/health`);
    console.log('✅ Federation registry is healthy:', healthResponse.data);
    console.log();

    // Test 2: Check current peers
    console.log('2️⃣ Checking current peers...');
    const peersResponse = await axios.get(`${FEDERATION_URL}/federation/peers`);
    console.log('📋 Current peers:', peersResponse.data);
    console.log();

    // Test 3: Check current rooms
    console.log('3️⃣ Checking current rooms...');
    const roomsResponse = await axios.get(`${FEDERATION_URL}/federation/rooms`);
    console.log('📋 Current rooms:', roomsResponse.data);
    console.log();

    // Test 4: Register a test peer
    console.log('4️⃣ Registering test peer...');
    try {
      const peerResponse = await axios.post(`${FEDERATION_URL}/federation/peers`, {
        name: 'test-peer',
        url: 'http://localhost:9999'
      });
      console.log('✅ Test peer registered:', peerResponse.data);
    } catch (error) {
      console.log('ℹ️ Test peer already exists or registration failed:', error.response?.data || error.message);
    }
    console.log();

    // Test 5: Register a test room
    console.log('5️⃣ Registering test room...');
    try {
      const roomResponse = await axios.post(`${FEDERATION_URL}/federation/rooms`, {
        roomId: TEST_ROOM_ID,
        name: 'Test Persistence Room',
        peerUrl: 'http://localhost:9999'
      });
      console.log('✅ Test room registered:', roomResponse.data);
    } catch (error) {
      console.log('ℹ️ Test room already exists or registration failed:', error.response?.data || error.message);
    }
    console.log();

    // Test 6: Check room peers
    console.log('6️⃣ Checking test room peers...');
    try {
      const roomPeersResponse = await axios.get(`${FEDERATION_URL}/federation/rooms/${TEST_ROOM_ID}/peers`);
      console.log('✅ Test room peers:', roomPeersResponse.data);
    } catch (error) {
      console.log('❌ Failed to get room peers:', error.response?.data || error.message);
    }
    console.log();

    // Test 7: Test message relay
    console.log('7️⃣ Testing message relay...');
    try {
      const relayResponse = await axios.post(`${FEDERATION_URL}/federation/relay-message`, {
        roomId: TEST_ROOM_ID,
        message: {
          text: 'Test persistence message',
          from: {
            username: 'test-user',
            platform: 'test'
          },
          timestamp: new Date().toISOString()
        },
        originatingPlatform: 'http://localhost:9999'
      });
      console.log('✅ Message relay test:', relayResponse.data);
    } catch (error) {
      console.log('ℹ️ Message relay test (expected to fail for inactive platforms):', error.response?.data || error.message);
    }
    console.log();

    console.log('🎉 Federation persistence test completed!');
    console.log('📝 To test persistence:');
    console.log('   1. Restart your backend (npm start)');
    console.log('   2. Run this script again');
    console.log('   3. Check that the test room and peers are still there');

  } catch (error) {
    console.error('❌ Federation persistence test failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure the federation registry is running on port 7300');
      console.log('   Start your backend with: npm start');
    }
  }
}

// Run the test
testFederationPersistence();
