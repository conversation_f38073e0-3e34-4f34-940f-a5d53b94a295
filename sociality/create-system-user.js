#!/usr/bin/env node

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import path from 'path';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const envPath = path.resolve(__dirname, '.env');
dotenv.config({ path: envPath });

// User model (simplified)
const userSchema = new mongoose.Schema({
  _id: {
    type: mongoose.Schema.Types.ObjectId,
    default: () => new mongoose.Types.ObjectId('000000000000000000000000')
  },
  username: {
    type: String,
    required: true,
    unique: true
  },
  email: {
    type: String,
    required: true,
    unique: true
  },
  name: {
    type: String,
    required: true
  },
  profilePic: {
    type: String,
    default: ""
  },
  bio: {
    type: String,
    default: ""
  },
  isSystem: {
    type: Boolean,
    default: false
  }
}, { timestamps: true });

const User = mongoose.model('User', userSchema);

async function createSystemUser() {
  try {
    console.log('🔧 Creating system user for cross-platform messaging...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');
    
    // Check if system user already exists
    const existingUser = await User.findById('000000000000000000000000');
    if (existingUser) {
      console.log('✅ System user already exists:', existingUser.username);
      return;
    }
    
    // Create system user
    const systemUser = new User({
      _id: new mongoose.Types.ObjectId('000000000000000000000000'),
      username: 'system',
      email: '<EMAIL>',
      name: 'System User',
      profilePic: '',
      bio: 'System user for cross-platform messaging',
      isSystem: true
    });
    
    await systemUser.save();
    console.log('✅ System user created successfully:', systemUser.username);
    
  } catch (error) {
    if (error.code === 11000) {
      console.log('✅ System user already exists (duplicate key)');
    } else {
      console.error('❌ Error creating system user:', error.message);
    }
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

createSystemUser();
