# 🚀 Sociality Platform Startup Guide

## Quick Start (Current Working Method)

### Main Backend with Cross-Platform Integration
```bash
cd sociality
npm start
```

This will start the main backend with integrated cross-platform messaging that includes:
- ✅ Telegram Bot initialized
- ✅ Discord Client initialized
- 🌐 Federation Registry running on port 7300
- 📱 Telegram Service running on port 7301
- 🎮 Discord Service running on port 7302
- ✅ All services registered with federation registry

## Simple Two-Command Startup (Recommended)

### Prerequisites
- Node.js and npm installed
- MongoDB connection (already configured)

### Start Services

**Terminal 1: Backend (Port 5000)**
```bash
cd sociality
npm start
```

**Terminal 2: Frontend (Port 7100)**
```bash
cd sociality/frontend
npm run dev
```

This starts everything you need:
- ✅ Main Backend with integrated cross-platform messaging
- ✅ Telegram Bot initialized
- ✅ Discord Client initialized
- 🌐 Federation Registry running on port 7300
- 📱 Telegram Service running on port 7301
- 🎮 Discord Service running on port 7302
- ✅ All services registered with federation registry
- 🌐 Frontend development server on port 7100

## Service URLs (When using npm start)

| Service | URL | Description |
|---------|-----|-------------|
| Main Backend | http://localhost:5000 | Core API server with integrated cross-platform |
| Federation Registry | http://localhost:7300 | Cross-platform coordinator (auto-started) |
| Telegram Platform | http://localhost:7301 | Telegram bot integration (auto-started) |
| Discord Platform | http://localhost:7302 | Discord bot integration (auto-started) |
| Frontend | http://localhost:7100 | Main web application (separate start) |

## Health Checks

Check if services are running:
```bash
curl http://localhost:7300/health  # Federation Registry
curl http://localhost:5000/health  # Main Backend
curl http://localhost:7301/health  # Telegram Platform
curl http://localhost:7302/health  # Discord Platform
```

## Bot Configuration

### Telegram Bot
- Bot Username: @sociality_telegram_bot
- Commands: Use `/start` to begin

### Discord Bot
- Use slash commands: `/create`, `/join`, `/rooms`, `/status`, `/leave`
- Invite the bot to your Discord server first

## Testing Cross-Platform Messaging

1. **Create a room** using Discord slash command: `/create MyRoom`
2. **Join the room** from Telegram: Send room ID to the bot
3. **Send messages** in either platform and watch them relay!

## Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Find and kill process using port
   lsof -ti:7300 | xargs kill -9
   ```

2. **Dependencies not installed**
   ```bash
   # Clean install
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **MongoDB connection issues**
   - Check your internet connection
   - Verify MongoDB URI in .env files

4. **Bot tokens invalid**
   - Check .env files for correct bot tokens
   - Regenerate tokens if needed

### Logs and Debugging

- Each service logs to its terminal tab
- Check individual tabs for error messages
- Use browser developer tools for frontend issues

## Environment Variables

### Main Backend (.env)
```
PORT=5000
MONGO_URI=mongodb+srv://...
JWT_SECRET=...
CLOUDINARY_CLOUD_NAME=...
FEDERATION_ENABLED=true
```

### Telegram Platform (.env)
```
PORT=7301
BOT_TOKEN=...
FEDERATION_REGISTRY_URL=http://localhost:7300
```

### Discord Platform (.env)
```
PORT=7302
BOT_TOKEN=...
CLIENT_ID=...
FEDERATION_REGISTRY_URL=http://localhost:7300
```

## Development Commands

```bash
# Start in development mode with auto-reload (backend)
npm run dev

# Install new dependencies
npm install <package-name>

# Build frontend for production
npm run build

# Migrate existing users (if needed)
npm run migrate:users
```

## Production Deployment

For production deployment, consider using:
- PM2 for process management
- Nginx for reverse proxy
- Docker for containerization
- Environment-specific .env files

---

🎉 **Your Sociality platform with cross-platform messaging is now ready!**
