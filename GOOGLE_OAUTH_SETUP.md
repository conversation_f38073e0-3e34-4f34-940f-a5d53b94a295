# Google OAuth2 Setup Instructions

## 1. Create Google OAuth2 Credentials

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API:
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API" and enable it
4. Create OAuth2 credentials:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Web application"
   - Add authorized redirect URIs:
     - `http://localhost:5000/api/auth/google/callback` (for development)
     - Add your production domain when deploying
5. Copy the Client ID and Client Secret

## 2. Update Environment Variables

Update your `.env` file with the actual Google OAuth credentials:

```env
# Replace these with your actual Google OAuth credentials
GOOGLE_CLIENT_ID=your_actual_google_client_id_here
GOOGLE_CLIENT_SECRET=your_actual_google_client_secret_here
SESSION_SECRET=your_random_session_secret_here
```

## 3. Generate Session Secret

You can generate a secure session secret using Node.js:

```javascript
require('crypto').randomBytes(64).toString('hex')
```

## 4. Test the Implementation

1. Start your backend server: `npm run dev`
2. Start your frontend: `cd frontend && npm run dev`
3. Go to `http://localhost:4000/auth`
4. Click "Continue with Google" button
5. Complete the OAuth flow

## 5. Features Implemented

- ✅ Google OAuth2 login integration
- ✅ User account linking (existing users can link Google accounts)
- ✅ New user creation via Google OAuth
- ✅ JWT token generation for OAuth users
- ✅ MongoDB Atlas connection configured
- ✅ Session management for OAuth flow
- ✅ Frontend OAuth callback handling
- ✅ Error handling for OAuth failures

## 6. Database Changes

The User model now includes:
- `googleId`: Stores Google user ID
- `isGoogleUser`: Boolean flag for Google OAuth users
- `password`: Now optional for Google OAuth users

## 7. Security Notes

- Sessions are configured with secure cookies in production
- JWT tokens are still used for API authentication
- Google OAuth provides additional security layer
- User emails are validated through Google's OAuth flow

## 8. Troubleshooting

- Make sure your Google OAuth redirect URI exactly matches what's configured in Google Cloud Console
- Check that the Google+ API is enabled in your Google Cloud project
- Verify that your environment variables are correctly set
- Check browser console for any JavaScript errors during OAuth flow
