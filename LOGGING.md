# Logging Configuration Guide

This document explains how to configure logging in the Sociality application.

## Log Levels

The application supports the following log levels:

- `NONE`: No logs will be displayed
- `ERROR`: Only error logs will be displayed
- `INFO`: Error and info logs will be displayed
- `DEBUG`: All logs (error, info, and debug) will be displayed

## Socket Logs

Socket-specific logs can be enabled or disabled separately using the `ENABLE_SOCKET_LOGS` environment variable.

## Configuration

To configure logging, add the following variables to your `.env` file:

```
# Log levels: NONE, ERROR, INFO, DEBUG
LOG_LEVEL=ERROR
# Set to 'true' to enable socket-specific logs, 'false' to disable
ENABLE_SOCKET_LOGS=false
```

### Examples

1. Minimal logging (recommended for production):
```
LOG_LEVEL=ERROR
ENABLE_SOCKET_LOGS=false
```

2. Standard logging (recommended for development):
```
LOG_LEVEL=INFO
ENABLE_SOCKET_LOGS=false
```

3. Verbose logging (for debugging):
```
LOG_LEVEL=DEBUG
ENABLE_SOCKET_LOGS=true
```

## Default Values

If not specified in the `.env` file:
- In development mode, the default log level is `INFO`
- In production mode, the default log level is `ERROR`
- Socket logs are disabled by default
